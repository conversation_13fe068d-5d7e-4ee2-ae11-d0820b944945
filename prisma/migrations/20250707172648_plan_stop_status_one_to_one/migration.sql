/*
  Warnings:

  - You are about to drop the column `plan_stop_id` on the `plan_stop_status` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[status_id]` on the table `plan_stop` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "plan_stop_status" DROP CONSTRAINT "plan_stop_status_plan_stop_id_fkey";

-- DropIndex
DROP INDEX "plan_stop_status_plan_stop_id_idx";

-- AlterTable
ALTER TABLE "plan_stop" ADD COLUMN     "status_id" TEXT;

-- AlterTable
ALTER TABLE "plan_stop_status" DROP COLUMN "plan_stop_id";

-- CreateIndex
CREATE UNIQUE INDEX "plan_stop_status_id_key" ON "plan_stop"("status_id");

-- AddForeignKey
ALTER TABLE "plan_stop" ADD CONSTRAINT "plan_stop_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "plan_stop_status"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
