generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_URL_POOLED")
  directUrl = env("POSTGRES_URL_NON_POOLING")
}

model POI {
  id        String  @id @default(cuid())
  name      String
  address   Address @relation(fields: [addressId], references: [id])
  addressId String  @unique

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@index([name])
  @@map("poi")
}

model Address {
  id String @id @default(cuid())

  name     String
  location String?
  lat      Float
  lng      Float

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  profile Profile?
  poi     POI?

  scheduleStopAddress ScheduleStop[] @relation("ScheduleStopAddress")
  planStopAddress     PlanStop[]     @relation("PlanStopAddress")
  school              School[]

  @@map("address")
}

enum BanReason {
  ON_DUTY
  INCOMPLETE_PROFILE
  INCOMPLETE_SCHEDULE
  MISSING_PAYMENT
  COOPERATION_TERMINATED
}

model Profile {
  id     String  @id @default(cuid())
  userId String? @unique @map("user_id")

  firstName String @map("first_name")
  lastName  String @map("last_name")

  avatarUrl     String?    @map("avatar_url")
  email         String
  phone         String?
  firebaseToken String?    @map("firebase_token")
  banReason     BanReason? @map("ban_reason")
  roles         String[]

  addressRelation Address? @relation(fields: [addressId], references: [id], onDelete: SetNull)
  addressId       String?  @unique @map("address_id")

  defaultPickupTime DateTime? @map("default_pickup_time")
  agreedGdpr        Boolean   @default(false) @map("agreed_gdpr")
  agreedTerms       Boolean   @default(false) @map("agreed_terms")

  fakturoidId BigInt? @map("fakturoid_id")

  onboardedAt  DateTime? @map("onboarded_at")
  registeredAt DateTime? @map("registered_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime? @updatedAt @map("updated_at")

  vehicle  Vehicle? @relation("Vehicle")
  children Child[]  @relation("Children")

  schedule                Schedule[]       @relation("DriverSchedule")
  updatedSchedules        Schedule[]       @relation("ScheduleUpdatedBy")
  updatedPlans            Plan[]           @relation("PlanUpdatedBy")
  plan                    Plan[]           @relation("DriverPlan")
  allocations             PlanAllocation[] @relation("DriverAllocation")
  createdPlanStopStatuses PlanStopStatus[] @relation("CreatedPlanStopStatuses")

  createdNotes   Note[]           @relation("CreatedNotes")
  userNotes      UserNote[]
  driverDocument DriverDocument[]
  driverLocation DriverLocation?

  @@unique(email)
  @@map("profile")
}

model DriverLocation {
  id String @id @default(cuid())

  latitude         Float
  longitude        Float
  accuracy         Float
  altitude         Float?
  altitudeAccuracy Float? @map("altitude_accuracy")
  heading          Float?
  speed            Float?

  timestamp DateTime

  driver   Profile @relation(fields: [driverId], references: [id], onDelete: Cascade)
  driverId String  @map("driver_id")

  createdAt DateTime @default(now()) @map("created_at")

  @@unique([driverId])
  @@map("driver_location")
}

model DriverDocument {
  id String @id @default(cuid())

  driver   Profile @relation(fields: [driverId], references: [id], onDelete: Cascade)
  driverId String  @map("driver_id")

  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentId String   @unique @map("document_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("driver_document")
}

model UserNote {
  id String @id @default(cuid())

  userProfile   Profile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)
  userProfileId String  @map("user_profile_id")

  note   Note   @relation(fields: [noteId], references: [id], onDelete: Cascade)
  noteId String @map("note_id")

  @@map("user_note")
}

model Note {
  id   String @id @default(cuid())
  text String

  createdBy   Profile? @relation("CreatedNotes", fields: [createdById], references: [id], onDelete: SetNull)
  createdById String?  @map("created_by_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  vehicleNotes VehicleNote[]
  userNotes    UserNote[]

  @@map("note")
}

model Document {
  id String @id @default(cuid())

  url         String
  description String

  vehicleDocument VehicleDocument?
  driverDocument  DriverDocument?

  @@map("document")
}

model VehicleDocument {
  id String @id @default(cuid())

  vehicle   Vehicle @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
  vehicleId String  @map("vehicle_id")

  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentId String   @unique @map("document_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("vehicle_document")
}

model Vehicle {
  id           String  @id @default(cuid())
  licencePlate String  @map("licence_plate")
  make         String
  model        String
  color        String?
  year         Int?

  capacity Int      @default(8)
  driver   Profile? @relation("Vehicle", fields: [driverId], references: [id])
  driverId String?  @unique @map("driver_id")

  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  vehicleDocuments VehicleDocument[]
  vehicleNotes     VehicleNote[]

  @@index([licencePlate])
  @@index([make, model])
  @@map("vehicle")
}

model VehicleNote {
  id String @id @default(cuid())

  vehicle   Vehicle @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
  vehicleId String  @map("vehicle_id")

  note   Note   @relation(fields: [noteId], references: [id], onDelete: Cascade)
  noteId String @map("note_id")

  @@map("vehicle_note")
}

model ChildSchool {
  child   Child  @relation(fields: [childId], references: [id])
  childId String @map("child_id")

  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  schoolId String @map("school_id")

  updatedAt DateTime @updatedAt @map("updated_at")

  @@id([childId, schoolId])
  @@unique(childId)
  @@map("child_school")
}

model Child {
  id        String @id @default(cuid())
  firstName String @default("") @map("first_name")
  lastName  String @default("") @map("last_name")

  avatarUrl String?   @map("avatar_url")
  dob       DateTime?
  grade     String?
  note      String?

  schedule Schedule[] @relation("ChildSchedule")
  plan     Plan[]     @relation("ChildPlan")

  client   Profile @relation("Children", fields: [clientId], references: [id], onDelete: Cascade)
  clientId String  @map("client_id")

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  childSchool ChildSchool[]

  @@index([clientId])
  @@index([deletedAt])
  @@map("child")
}

enum SettingsType {
  FAKTUROID
}

model Settings {
  id String @id @default(cuid())

  type  SettingsType
  value String

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique(type)
  @@map("settings")
}

model Holiday {
  id String @id @default(cuid())

  name String?

  school   School @relation("SchoolHoliday", fields: [schoolId], references: [id], onDelete: Cascade)
  schoolId String @map("school_id")

  startDate DateTime @map("start_date")
  endDate   DateTime @map("end_date")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([schoolId])
  @@map("holiday")
}

model School {
  id String @id @default(cuid())

  name      String
  address   Address? @relation(fields: [addressId], references: [id], onDelete: SetNull)
  addressId String?  @map("address_id")

  holiday Holiday[] @relation("SchoolHoliday")

  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")
  childSchool ChildSchool[]

  @@index([name])
  @@map("school")
}

model Schedule {
  id String @id @default(cuid())

  child   Child  @relation("ChildSchedule", fields: [childId], references: [id], onDelete: Cascade)
  childId String @map("child_id")

  selfManaged Boolean @default(false) @map("self_managed")

  driver   Profile? @relation("DriverSchedule", fields: [driverId], references: [id], onDelete: SetNull)
  driverId String?  @map("driver_id")

  dayOfWeek Int @map("day_of_week")

  hours   Int
  minutes Int

  allocation   ScheduleAllocation? @relation(fields: [allocationId], references: [id], onDelete: SetNull)
  allocationId String?             @map("allocation_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  updatedBy   Profile? @relation("ScheduleUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  updatedById String?  @map("updated_by_id")

  stops ScheduleStop[]
  plans Plan[]

  @@index([childId])
  @@index([driverId])
  @@index([dayOfWeek])
  @@index([allocationId])
  @@index([childId, dayOfWeek])
  @@map("schedule")
}

enum ScheduleStopType {
  PICK_UP
  DROP_OFF
}

model ScheduleStop {
  id        String           @id @default(cuid())
  type      ScheduleStopType
  address   Address?         @relation("ScheduleStopAddress", fields: [addressId], references: [id], onDelete: SetNull)
  addressId String?          @map("address_id")
  order     Int              @default(0)

  schedule   Schedule @relation(fields: [scheduleId], references: [id], onDelete: Cascade)
  scheduleId String   @map("schedule_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  planStops PlanStop[]

  @@unique([scheduleId, type])
  @@index([scheduleId])
  @@index([updatedAt])
  @@map("schedule_stop")
}

model ScheduleAllocation {
  id       String     @id @default(cuid())
  schedule Schedule[]
  label    String

  createdAt       DateTime         @default(now()) @map("created_at")
  updatedAt       DateTime         @updatedAt @map("updated_at")
  planAllocations PlanAllocation[]

  @@map("schedule_allocation")
}

model Plan {
  id String @id @default(cuid())

  child   Child  @relation("ChildPlan", fields: [childId], references: [id], onDelete: Cascade)
  childId String @map("child_id")

  driver   Profile? @relation("DriverPlan", fields: [driverId], references: [id], onDelete: SetNull)
  driverId String?  @map("driver_id")

  dateTime DateTime @map("date_time")

  schedule   Schedule? @relation(fields: [scheduleId], references: [id], onDelete: SetNull)
  scheduleId String?   @map("schedule_id")

  allocation   PlanAllocation? @relation(fields: [allocationId], references: [id], onDelete: SetNull)
  allocationId String?         @map("allocation_id")

  deletedAt DateTime? @map("deleted_at")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")

  updatedBy   Profile? @relation("PlanUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  updatedById String?  @map("updated_by_id")

  stops PlanStop[]

  @@index([childId, dateTime])
  @@index([driverId, dateTime])
  @@index([driverId, deletedAt])
  @@index([allocationId, deletedAt])
  @@index([dateTime])
  @@index([scheduleId])
  @@map("plan")
}

enum PlanStopStatusType {
  COMPLETED
  NO_SHOW
}

model PlanStopStatus {
  id   String             @id @default(cuid())
  type PlanStopStatusType

  createdBy   Profile? @relation("CreatedPlanStopStatuses", fields: [createdById], references: [id], onDelete: SetNull)
  createdById String?  @map("created_by_id")

  createdAt DateTime  @default(now()) @map("created_at")
  planStop  PlanStop?

  @@index([createdById])
  @@index([createdAt])
  @@map("plan_stop_status")
}

model PlanStop {
  id        String           @id @default(cuid())
  type      ScheduleStopType
  address   Address?         @relation("PlanStopAddress", fields: [addressId], references: [id], onDelete: SetNull)
  addressId String?          @map("address_id")
  order     Int              @default(0)

  plan   Plan   @relation(fields: [planId], references: [id], onDelete: Cascade)
  planId String @map("plan_id")

  scheduleStop   ScheduleStop? @relation(fields: [scheduleStopId], references: [id], onDelete: SetNull)
  scheduleStopId String?       @map("schedule_stop_id")

  status   PlanStopStatus? @relation(fields: [statusId], references: [id], onDelete: Restrict)
  statusId String?         @unique @map("status_id")

  eta DateTime?

  completedAt DateTime? @map("completed_at")
  arrivedAt   DateTime? @map("arrived_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@unique([planId, type])
  @@index([planId])
  @@index([scheduleStopId])
  @@map("plan_stop")
}

enum TerminationReason {
  VEHICLE_BREAKDOWN
  DISPATCHER_CANCELLED
  PARENT_PICKED_UP
  STARTED_BY_ACCIDENT
  OTHER
}

model PlanAllocation {
  id    String @id @default(cuid())
  plan  Plan[]
  label String

  date DateTime

  driver   Profile @relation("DriverAllocation", fields: [driverId], references: [id], onDelete: Cascade)
  driverId String  @map("driver_id")

  scheduleAllocation   ScheduleAllocation? @relation(fields: [scheduleAllocationId], references: [id], onDelete: SetNull)
  scheduleAllocationId String?             @map("schedule_allocation_id")

  route        Json?
  routePreview Json? @map("route_preview")
  routeTaken   Json? @map("route_taken")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  startedAt   DateTime? @map("started_at")
  completedAt DateTime? @map("completed_at")

  terminationReason TerminationReason? @map("termination_reason")
  terminatedAt      DateTime?          @map("terminated_at")

  @@unique([date, scheduleAllocationId])
  @@index([date])
  @@index([driverId])
  @@index([driverId, date])
  @@index([startedAt])
  @@map("plan_allocation")
}

enum MonitoringType {
  PLAN
  PLAN_UPDATER
  PLAN_CREATOR
  HOLIDAY
}

model Monitoring {
  id   String         @id @default(cuid())
  type MonitoringType

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique(type)
  @@index([createdAt])
  @@map("monitoring")
}
