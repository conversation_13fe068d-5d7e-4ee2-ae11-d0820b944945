import { db } from '@/module/app/db';
import { driverRepository } from '@/module/driver/repository/driver-repository';
import { planAllocationRepository } from '@/module/plan/repository/plan-allocation-repository';
import { pushVehicleAssigned } from '@/module/vehicle/notifications/push-vehicle-assigned';
import { pushVehicleUnassigned } from '@/module/vehicle/notifications/push-vehicle-unassigned';

type Props = {
  driverId: string;
  vehicleId: string;
};

export const assignVehicle = async ({ driverId, vehicleId }: Props) => {
  const repository = driverRepository(db);

  const originalAssignee = await repository.findByVehicleId(vehicleId);
  const newAssignee = await repository.findById(driverId);

  if (!newAssignee) {
    throw new Error('Unable to assign vehicle: assignee not found');
  }

  if (originalAssignee) {
    // Find original assignee ongoing rides
    const count = await planAllocationRepository(db).countDriverOngoing(
      originalAssignee.id
    );

    if (count) {
      throw new Error(
        'Unable to assign vehicle: original assignee has a ride in progress.'
      );
    }
  }

  // Find new assignee ongoing rides
  const count = await planAllocationRepository(db).countDriverOngoing(
    newAssignee.id
  );

  if (count) {
    throw new Error(
      'Unable to assign vehicle: assignee has a ride in progress.'
    );
  }

  await repository.assignVehicle(driverId, vehicleId);

  if (originalAssignee?.firebaseToken) {
    await pushVehicleUnassigned({
      firebaseToken: originalAssignee?.firebaseToken,
    });
  }

  if (newAssignee.firebaseToken) {
    await pushVehicleAssigned({
      firebaseToken: newAssignee.firebaseToken,
      licencePlate: newAssignee.vehicle?.licencePlate,
    });
  }

  return newAssignee;
};
