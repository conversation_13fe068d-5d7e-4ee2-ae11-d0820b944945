import { pushNotification } from '@/module/notification/core/push-notification';

type Args = {
  licencePlate?: string;
  firebaseToken: string;
};

export const pushVehicleAssigned = ({ firebaseToken, licencePlate }: Args) => {
  return pushNotification({
    title: 'Panda RIDE: Vehicle update',
    body: `Vehicle "${licencePlate}" has been assigned.`,
    token: firebaseToken,
    data: { type: 'VEHICLE_ASSIGNED' },
  });
};
