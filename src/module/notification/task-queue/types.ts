import { z } from 'zod';

export enum NotificationType {
  ALLOCATION_STARTED = 'ALLOCATION_STARTED',
  ALLOCATION_STOP_COMPLETED = 'ALLOCATION_STOP_COMPLETED',
}

const allocationStartedBodySchema = z.object({
  type: z.literal(NotificationType.ALLOCATION_STARTED),
  allocationId: z.string(),
});

const allocationStopCompletedBodySchema = z.object({
  type: z.literal(NotificationType.ALLOCATION_STOP_COMPLETED),
  allocationId: z.string(),
  stopId: z.string(),
  nextStopId: z.string().optional(),
});

export const notificationBodySchema = z.union([
  allocationStartedBodySchema,
  allocationStopCompletedBodySchema,
]);

export type NotificationBody = z.infer<typeof notificationBodySchema>;
