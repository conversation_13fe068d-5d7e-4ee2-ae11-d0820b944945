import { createAdminClient } from '@/module/app/supabase-admin';
import {
  NOTIFICATION_MQ_QUEUE,
  NOTIFICATION_MQ_SCHEMA,
} from '@/module/notification/task-queue/config';
import { NotificationBody } from '@/module/notification/task-queue/types';

export const publishNotification = async (
  notificationBody: NotificationBody
) => {
  const client = await createAdminClient();

  const result = await client.schema(NOTIFICATION_MQ_SCHEMA).rpc('send', {
    queue_name: NOTIFICATION_MQ_QUEUE,
    message: notificationBody,
  });

  if (result.error) {
    throw new Error(result.error.message);
  }
};
