import { createAdminClient } from '@/module/app/supabase-admin';
import {
  NOTIFICATION_MQ_QUEUE,
  NOTIFICATION_MQ_SCHEMA,
} from '@/module/notification/task-queue/config';

export const completeNotification = async (messageId: number) => {
  const client = await createAdminClient();
  await client.schema(NOTIFICATION_MQ_SCHEMA).rpc('delete', {
    queue_name: NOTIFICATION_MQ_QUEUE,
    message_id: messageId,
  });
};
