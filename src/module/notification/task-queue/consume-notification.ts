import { createAdminClient } from '@/module/app/supabase-admin';
import {
  NOTIFICATION_MQ_QUEUE,
  NOTIFICATION_MQ_SCHEMA,
} from '@/module/notification/task-queue/config';
import {
  NotificationBody,
  notificationBodySchema,
} from '@/module/notification/task-queue/types';

export const consumeNotification = async (): Promise<
  | {
      messageId: number;
      body: NotificationBody;
    }
  | undefined
> => {
  const client = await createAdminClient();
  const result = await client.schema(NOTIFICATION_MQ_SCHEMA).rpc('read', {
    queue_name: NOTIFICATION_MQ_QUEUE,
    sleep_seconds: 10,
    n: 1,
  });

  if (result.error) {
    throw new Error(result.error.message);
  }

  const data = result.data[0];

  if (!data) {
    return;
  }

  return {
    messageId: data.msg_id,
    body: notificationBodySchema.parse(data.message),
  };
};
