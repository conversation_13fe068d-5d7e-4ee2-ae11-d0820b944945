import { NextRequest } from 'next/server';

export interface AuthResult {
  success: boolean;
  error?: string;
}

/**
 * Validates authorization for notification endpoints.
 * In development mode, authorization is bypassed.
 * In production, requires a valid Bearer token matching CRON_SECRET.
 */
export function validateAuthorization(req: NextRequest): AuthResult {
  // Skip authorization in development
  if (process.env.NODE_ENV === 'development') {
    return { success: true };
  }

  const authHeader = req.headers.get('Authorization');
  const expectedToken = `Bearer ${process.env.CRON_SECRET}`;

  if (!authHeader) {
    return {
      success: false,
      error: 'Missing Authorization header',
    };
  }

  if (authHeader !== expectedToken) {
    return {
      success: false,
      error: 'Invalid authorization token',
    };
  }

  return { success: true };
}
