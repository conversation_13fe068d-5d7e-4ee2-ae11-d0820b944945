import { db } from '@/module/app/db';
import { AllocationStartedHandler } from '@/module/notification/handlers/allocation-started-handler';
import { AllocationStopCompletedHandler } from '@/module/notification/handlers/allocation-stop-completed-handler';
import {
  NotificationContext,
  NotificationHandler,
} from '@/module/notification/handlers/base-handler';
import { completeNotification } from '@/module/notification/task-queue/complete-notification';
import { consumeNotification } from '@/module/notification/task-queue/consume-notification';
import { NotificationType } from '@/module/notification/task-queue/types';
import { ProcessingResult } from '@/module/notification/utils/response';

/**
 * Main service for processing notifications from the message queue.
 *
 * This service:
 * - Consumes notifications from the queue
 * - Routes them to appropriate handlers based on type
 * - Manages notification completion
 * - Provides error handling and logging
 */
export class NotificationProcessor {
  private handlers: Map<NotificationType, NotificationHandler>;

  constructor() {
    this.handlers = new Map([
      [NotificationType.ALLOCATION_STARTED, new AllocationStartedHandler()],
      [
        NotificationType.ALLOCATION_STOP_COMPLETED,
        new AllocationStopCompletedHandler(),
      ],
    ]);
  }

  /**
   * Processes the next notification from the queue.
   * @returns ProcessingResult indicating what was processed
   */
  async processNext(): Promise<ProcessingResult> {
    // Consume next notification from queue
    const notification = await consumeNotification();

    if (!notification) {
      return {
        processed: false,
        message: 'No notifications in queue',
      };
    }

    try {
      // Get allocation data
      const allocation = await this.getAllocation(
        notification.body.allocationId
      );

      // Create context for handlers
      const context: NotificationContext = {
        messageId: notification.messageId,
        body: notification.body,
        allocation,
      };

      // Process the notification
      const result = await this.processNotification(context);

      // Complete notification if processing was successful
      if (result.shouldComplete) {
        await completeNotification(notification.messageId);
      }

      return {
        processed: true,
        notificationType: notification.body.type,
        allocationId: notification.body.allocationId,
        message: result.success ? 'Processed successfully' : result.error,
      };
    } catch (error) {
      console.error('Error processing notification:', error);
      throw error;
    }
  }

  /**
   * Routes notification to appropriate handler and processes it.
   */
  private async processNotification(context: NotificationContext) {
    const handler = this.handlers.get(context.body.type);

    if (!handler) {
      throw new Error(
        `No handler found for notification type: ${context.body.type}`
      );
    }

    return await handler.handle(context);
  }

  /**
   * Retrieves allocation data from database.
   */
  private async getAllocation(allocationId: string) {
    const allocation = await db.planAllocation.findUnique({
      where: { id: allocationId },
      select: {
        id: true,
        route: true,
        driverId: true,
      },
    });

    if (!allocation) {
      throw new Error(`Allocation not found: ${allocationId}`);
    }

    return allocation;
  }

  /**
   * Registers a new handler for a notification type.
   * Useful for extending the processor with new notification types.
   */
  registerHandler(type: NotificationType, handler: NotificationHandler): void {
    this.handlers.set(type, handler);
  }

  /**
   * Gets all registered notification types.
   */
  getSupportedTypes(): NotificationType[] {
    return Array.from(this.handlers.keys());
  }
}
