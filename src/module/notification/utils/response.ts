import { NextResponse } from 'next/server';

export interface ProcessingResult {
  processed: boolean;
  notificationType?: string;
  allocationId?: string;
  message?: string;
}

/**
 * Creates a standardized success response for the notification API.
 */
export function createSuccessResponse(result: ProcessingResult): NextResponse {
  return NextResponse.json({
    ok: true,
    ...result,
  });
}

/**
 * Creates a standardized error response for the notification API.
 */
export function createErrorResponse(
  message: string,
  status: number = 500
): NextResponse {
  return NextResponse.json(
    {
      ok: false,
      error: message,
    },
    { status }
  );
}
