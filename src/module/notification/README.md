# Notification Processing System

This module provides a scalable, modular system for processing notifications from the message queue.

## Architecture

### Core Components

1. **NotificationProcessor** (`services/notification-processor.ts`)
   - Main orchestrator for notification processing
   - Manages handler registration and routing
   - Handles notification consumption and completion

2. **Notification Handlers** (`handlers/`)
   - `base-handler.ts` - Interface and types for all handlers
   - `allocation-started-handler.ts` - Handles allocation start notifications
   - `allocation-stop-completed-handler.ts` - Handles stop completion notifications

3. **Middleware** (`middleware/`)
   - `auth.ts` - Authorization validation for API endpoints

4. **Utilities** (`utils/`)
   - `response.ts` - Standardized API response helpers

## Usage

### Adding a New Notification Type

1. **Define the notification type** in `task-queue/types.ts`
2. **Create a handler** implementing `NotificationHandler`:

```typescript
export class MyNewHandler implements NotificationHandler {
  async handle(context: NotificationContext): Promise<HandlerResult> {
    // Your processing logic here
    return {
      success: true,
      shouldComplete: true,
    };
  }
}
```

3. **Register the handler** in `NotificationProcessor`:

```typescript
// In constructor
this.handlers.set(NotificationType.MY_NEW_TYPE, new MyNewHandler());
```

### Handler Guidelines

- **Single Responsibility**: Each handler should handle one notification type
- **Error Handling**: Always wrap logic in try-catch and return appropriate results
- **Validation**: Validate notification type at the start of handle method
- **Logging**: Log errors for debugging
### Response Patterns

Handlers should return:
- `{ success: true, shouldComplete: true }` - Normal successful processing
- `{ success: true, shouldComplete: false }` - Processing succeeded but don't complete notification (e.g., route update)
- `{ success: false, shouldComplete: false, error: "message" }` - Error occurred

## Benefits

1. **Modularity**: Each notification type has its own handler
2. **Testability**: Handlers can be unit tested independently
3. **Scalability**: Easy to add new notification types
4. **Maintainability**: Clear separation of concerns
5. **Error Handling**: Centralized error handling with proper logging
6. **Type Safety**: Full TypeScript support with proper interfaces
