import { validateAuthorization } from '../middleware/auth';

// Mock environment variables
const originalEnv = process.env;

// Mock NextRequest
const createMockRequest = (headers: Record<string, string> = {}) => ({
  headers: {
    get: (name: string) => headers[name] || null,
  },
});

describe('validateAuthorization', () => {
  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('in development mode', () => {
    beforeEach(() => {
      process.env = { ...originalEnv, NODE_ENV: 'development' };
    });

    it('should allow requests without authorization', () => {
      const req = createMockRequest() as any;
      const result = validateAuthorization(req);

      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
    });
  });

  describe('in production mode', () => {
    beforeEach(() => {
      process.env = {
        ...originalEnv,
        NODE_ENV: 'production',
        CRON_SECRET: 'test-secret'
      };
    });

    it('should reject requests without Authorization header', () => {
      const req = createMockRequest() as any;
      const result = validateAuthorization(req);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Missing Authorization header');
    });

    it('should reject requests with invalid token', () => {
      const req = createMockRequest({
        'Authorization': 'Bearer invalid-token',
      }) as any;
      const result = validateAuthorization(req);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid authorization token');
    });

    it('should accept requests with valid token', () => {
      const req = createMockRequest({
        'Authorization': 'Bearer test-secret',
      }) as any;
      const result = validateAuthorization(req);

      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
    });
  });
});
