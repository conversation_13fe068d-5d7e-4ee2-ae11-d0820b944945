import { NotificationProcessor } from '../services/notification-processor';
import { NotificationType } from '../task-queue/types';
import { AllocationStartedHandler } from '../handlers/allocation-started-handler';
import { AllocationStopCompletedHandler } from '../handlers/allocation-stop-completed-handler';

// Mock the dependencies
jest.mock('../task-queue/consume-notification');
jest.mock('../task-queue/complete-notification');
jest.mock('@/module/app/db');

describe('NotificationProcessor', () => {
  let processor: NotificationProcessor;

  beforeEach(() => {
    processor = new NotificationProcessor();
  });

  describe('constructor', () => {
    it('should register default handlers', () => {
      const supportedTypes = processor.getSupportedTypes();
      
      expect(supportedTypes).toContain(NotificationType.ALLOCATION_STARTED);
      expect(supportedTypes).toContain(NotificationType.ALLOCATION_STOP_COMPLETED);
      expect(supportedTypes).toHaveLength(2);
    });
  });

  describe('registerHandler', () => {
    it('should allow registering new handlers', () => {
      const mockHandler = {
        handle: jest.fn(),
      };

      processor.registerHandler(NotificationType.ALLOCATION_STARTED, mockHandler);
      
      const supportedTypes = processor.getSupportedTypes();
      expect(supportedTypes).toContain(NotificationType.ALLOCATION_STARTED);
    });
  });

  describe('getSupportedTypes', () => {
    it('should return all registered notification types', () => {
      const types = processor.getSupportedTypes();
      
      expect(Array.isArray(types)).toBe(true);
      expect(types.length).toBeGreaterThan(0);
    });
  });
});

describe('Handler Registration', () => {
  it('should have correct handler types registered', () => {
    const processor = new NotificationProcessor();
    const types = processor.getSupportedTypes();

    // Verify we have the expected notification types
    expect(types).toEqual(
      expect.arrayContaining([
        NotificationType.ALLOCATION_STARTED,
        NotificationType.ALLOCATION_STOP_COMPLETED,
      ])
    );
  });
});
