import * as Sentry from '@sentry/nextjs';
import admin from 'firebase-admin';

import { Maybe } from '@/lib/maybe';
import { Logger } from '@/module/logger';

const serviceAccount = JSON.parse(
  Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT!, 'base64').toString('utf8')
);

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

type Props = {
  token: Maybe<string>;
  title: string;
  body: string;
  data?: { [key: string]: string };
};

export const pushNotification = async ({ token, title, body, data }: Props) => {
  try {
    Logger.debug('pushNotification', { token, title, body, data });

    if (token) {
      await admin.messaging().send({
        notification: { title, body },
        data,
        token,
      });
    }
  } catch (e) {
    Sentry.captureException(e);
  }
};
