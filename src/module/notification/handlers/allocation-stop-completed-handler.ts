import { db } from '@/module/app/db';
import { NotificationType } from '@/module/notification/task-queue/types';
import { pushCompletedPickup } from '@/module/plan/notifications/push-completed-pickup';
import { pushDropoffCompleted } from '@/module/plan/notifications/push-dropoff-completed';
import { pushUpcomingDropoff } from '@/module/plan/notifications/push-upcoming-dropoff';
import { pushUpcomingPickup } from '@/module/plan/notifications/push-upcoming-pickup';
import { isReturnAllocation } from '@/module/plan/services/is-return-allocation';
import { publishRouteUpdate } from '@/module/plan/task-queue/route-planning';

import {
  HandlerResult,
  NotificationContext,
  NotificationHandler,
} from './base-handler';

/**
 * Handles ALLOCATION_STOP_COMPLETED notifications.
 *
 * When a stop is completed:
 * - If no route exists: Trigger route update and return early
 * - If there's a next stop: Push upcoming pickup notification
 * - For return allocations with next stop: Also push completed pickup and upcoming dropoff
 * - If no next stop: Push dropoff completed notification (final stop)
 */
export class AllocationStopCompletedHandler implements NotificationHandler {
  async handle(context: NotificationContext): Promise<HandlerResult> {
    try {
      // Validate notification type
      if (context.body.type !== NotificationType.ALLOCATION_STOP_COMPLETED) {
        return {
          success: false,
          shouldComplete: false,
          error: `Invalid notification type for AllocationStopCompletedHandler: ${context.body.type}`,
        };
      }

      // If no route exists, trigger route update and return early
      if (!context.allocation.route) {
        await publishRouteUpdate(context.allocation.id);
        return {
          success: true,
          shouldComplete: false,
        };
      }

      const { nextStopId, stopId, allocationId } = context.body;

      if (nextStopId) {
        // There's a next stop - handle intermediate stop completion
        await this.handleIntermediateStop(allocationId, stopId, nextStopId);
      } else {
        // No next stop - this is the final stop
        await this.handleFinalStop(context.allocation.id, stopId);
      }

      return {
        success: true,
        shouldComplete: true,
      };
    } catch (error) {
      console.error(
        'Error handling allocation stop completed notification:',
        error
      );
      return {
        success: false,
        shouldComplete: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Handles completion of an intermediate stop (not the final stop).
   */
  private async handleIntermediateStop(
    allocationId: string,
    stopId: string,
    nextStopId: string
  ): Promise<void> {
    const returnAllocation = await isReturnAllocation(db, allocationId);

    // Push upcoming pickup for the next stop
    await pushUpcomingPickup(allocationId, nextStopId);

    // For return allocations, also handle pickup completion and upcoming dropoff
    if (returnAllocation) {
      await pushCompletedPickup(allocationId, stopId);
      await pushUpcomingDropoff(allocationId);
    }
  }

  /**
   * Handles completion of the final stop in the allocation.
   */
  private async handleFinalStop(
    allocationId: string,
    stopId: string
  ): Promise<void> {
    await pushDropoffCompleted(allocationId, stopId);
  }
}
