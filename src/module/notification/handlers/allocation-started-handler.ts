import { db } from '@/module/app/db';
import { NotificationType } from '@/module/notification/task-queue/types';
import { pushUpcomingPickup } from '@/module/plan/notifications/push-upcoming-pickup';
import { isReturnAllocation } from '@/module/plan/services/is-return-allocation';

import {
  HandlerResult,
  NotificationContext,
  NotificationHandler,
} from './base-handler';

/**
 * Handles ALLOCATION_STARTED notifications.
 *
 * When an allocation starts:
 * - For non-return allocations: Push upcoming pickup notification
 * - For return allocations: Skip pickup notification (they're already picked up)
 */
export class AllocationStartedHandler implements NotificationHandler {
  async handle(context: NotificationContext): Promise<HandlerResult> {
    try {
      // Validate notification type
      if (context.body.type !== NotificationType.ALLOCATION_STARTED) {
        return {
          success: false,
          shouldComplete: false,
          error: `Invalid notification type for AllocationStartedHandler: ${context.body.type}`,
        };
      }

      // Check if this is a return allocation
      const returnAllocation = await isReturnAllocation(
        db,
        context.allocation.id
      );

      // Only push upcoming pickup for non-return allocations
      if (!returnAllocation) {
        await pushUpcomingPickup(context.allocation.id);
      }

      return {
        success: true,
        shouldComplete: true,
      };
    } catch (error) {
      console.error('Error handling allocation started notification:', error);
      return {
        success: false,
        shouldComplete: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
