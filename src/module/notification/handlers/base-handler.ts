import { JsonValue } from '@prisma/client/runtime/library';

import { NotificationBody } from '@/module/notification/task-queue/types';

export interface NotificationContext {
  messageId: number;
  body: NotificationBody;
  allocation: {
    id: string;
    route: JsonValue | null;
    driverId: string;
  };
}

export interface HandlerResult {
  success: boolean;
  shouldComplete: boolean;
  error?: string;
}

/**
 * Base interface for notification handlers.
 * Each notification type should implement this interface.
 */
export interface NotificationHandler {
  /**
   * Handles the notification processing logic.
   * @param context - The notification context including message, body, and allocation
   * @returns Promise<HandlerResult> - Result indicating success and whether to complete the notification
   */
  handle(context: NotificationContext): Promise<HandlerResult>;
}
