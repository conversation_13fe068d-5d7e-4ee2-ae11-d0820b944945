'use server';

import { deduplicate } from '@/lib/deduplicate';
import { exists } from '@/lib/exists';
import { db } from '@/module/app/db';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';
import { getAdminProfile } from '@/module/profile/actions/current-profile';

export type UpdateData = {
  id: string;
  allocation: {
    id: string;
    label: string;
  } | null;
};

export const updateDetailedPlan = async (
  date: Date,
  driverId: string,
  plans: Array<UpdateData>
) => {
  const adminProfile = await getAdminProfile();

  if (!adminProfile) {
    throw new Error('Not authorized');
  }

  const allocations = plans.map((plan) => plan.allocation).filter(exists);

  await db.$transaction(async (tx) => {
    // Create or update plan allocations
    await Promise.all(
      allocations.map((allocation) =>
        tx.planAllocation.upsert({
          where: { id: allocation.id },
          create: {
            id: allocation.id,
            date,
            driverId,
            label: allocation.label,
          },
          update: {
            label: allocation.label,
          },
        })
      )
    );

    // Update allocations
    await Promise.all(
      plans.map((plan) =>
        tx.plan.update({
          where: { id: plan.id },
          data: { allocationId: plan.allocation?.id ?? null },
        })
      )
    );

    const allocationIds = deduplicate(
      allocations.map((allocation) => allocation.id)
    );

    // Update ride stop order
    await Promise.all(allocationIds.map(publishAllocationReset));
  });
};
