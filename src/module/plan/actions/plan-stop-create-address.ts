'use server';

import { ScheduleStopType } from '@prisma/client';

import { AddressLookup } from '@/module/address/lib/address-lookup';
import { db } from '@/module/app/db';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';
import { getAdminProfile } from '@/module/profile/actions/current-profile';

type Props = {
  planId: string;
  type: ScheduleStopType;
  address: AddressLookup;
};

export const planStopCreateAddress = async ({
  planId,
  type,
  address,
}: Props) => {
  const adminProfile = await getAdminProfile();

  if (!adminProfile) {
    throw new Error('Not authorized');
  }

  const updatedStop = await db.planStop.create({
    data: {
      type,
      address: { create: address },
      plan: { connect: { id: planId } },
    },
    include: { plan: true },
  });

  // Reset stop order on allocation
  if (updatedStop.plan.allocationId) {
    await publishAllocationReset(updatedStop.plan.allocationId);
  }

  return updatedStop;
};
