'use server';

import { db } from '@/module/app/db';
import { findAllocationId } from '@/module/plan/services/find-allocation-id';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';
import { getAdminProfile } from '@/module/profile/actions/current-profile';

type Props = {
  planId: string;
  driverId: string | null;
};

export const assignPlanDriver = async ({ planId, driverId }: Props) => {
  const adminProfile = await getAdminProfile();

  if (!adminProfile) {
    throw new Error('Not authorized');
  }

  const planToUpdate = await db.plan.findUniqueOrThrow({
    where: { id: planId },
  });

  const updatedPlan = await db.$transaction(async (tx) => {
    // Find suitable allocation id for new driver
    const allocationId = driverId
      ? await findAllocationId(tx, {
          driverId,
          dateTime: planToUpdate.dateTime,
        })
      : null;

    // Update plan
    return tx.plan.update({
      where: { id: planId },
      data: {
        driverId,
        allocationId,
      },
    });
  });

  // Reset stop order on target allocation
  if (planToUpdate.allocationId) {
    await publishAllocationReset(planToUpdate.allocationId);
  }

  // Reset stop order on destination allocation
  if (updatedPlan.allocationId) {
    await publishAllocationReset(updatedPlan.allocationId);
  }

  return updatedPlan;
};
