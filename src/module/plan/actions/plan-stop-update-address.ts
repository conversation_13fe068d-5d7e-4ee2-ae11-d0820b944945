'use server';

import { AddressLookup } from '@/module/address/lib/address-lookup';
import { db } from '@/module/app/db';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';
import { getAdminProfile } from '@/module/profile/actions/current-profile';

type Props = {
  planStopId: string;
  address: AddressLookup;
};

export const planStopUpdateAddress = async ({ planStopId, address }: Props) => {
  const adminProfile = await getAdminProfile();

  if (!adminProfile) {
    throw new Error('Not authorized');
  }

  const updatedStop = await db.planStop.update({
    where: { id: planStopId },
    data: { address: { create: address } },
    include: { plan: true },
  });

  if (updatedStop.plan.allocationId) {
    await publishAllocationReset(updatedStop.plan.allocationId);
  }

  return updatedStop;
};
