'use server';

import { getHours, getMinutes, getSeconds, set } from 'date-fns';

import { db } from '@/module/app/db';
import { findAllocationId } from '@/module/plan/services/find-allocation-id';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';
import { getAdminProfile } from '@/module/profile/actions/current-profile';

type Props = {
  resetDriver?: boolean;
  planId: string;
  dateTime: Date;
};

export const updatePlanTime = async ({
  planId,
  dateTime,
  resetDriver,
}: Props) => {
  const adminProfile = await getAdminProfile();

  if (!adminProfile) {
    throw new Error('Not authorized');
  }

  const planToUpdate = await db.plan.findUniqueOrThrow({
    where: { id: planId },
  });

  const updatedPlan = await db.$transaction(async (tx) => {
    const driverId = resetDriver ? null : planToUpdate.driverId;

    const normalizedDateTime = set(planToUpdate.dateTime, {
      hours: getHours(dateTime),
      minutes: getMinutes(dateTime),
      seconds: getSeconds(dateTime),
    });

    // Find suitable allocation id for new time
    const allocationId = await findAllocationId(tx, {
      dateTime: normalizedDateTime,
      driverId,
    });

    // Update plan
    return await tx.plan.update({
      where: { id: planId },
      data: {
        dateTime: normalizedDateTime,
        driverId,
        allocationId,
      },
    });
  });

  // Reset stop order on target allocation
  if (planToUpdate.allocationId) {
    await publishAllocationReset(planToUpdate.allocationId);
  }

  // Reset stop order on destination allocation
  if (updatedPlan.allocationId) {
    await publishAllocationReset(updatedPlan.allocationId);
  }

  return updatedPlan;
};
