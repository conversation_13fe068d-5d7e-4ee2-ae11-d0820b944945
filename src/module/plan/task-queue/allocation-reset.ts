import { createAdminClient } from '@/module/app/supabase-admin';

const SCHEMA = 'pgmq_public';
const QUEUE = 'plan_allocation_reset';

export const publishAllocationReset = async (allocationId: string) => {
  const client = await createAdminClient();

  const result = await client.schema(SCHEMA).rpc('send', {
    queue_name: QUEUE,
    message: { allocationId },
  });

  if (result.error) {
    throw new Error(result.error.message);
  }
};

export const consumeAllocationReset = async () => {
  const client = await createAdminClient();
  const result = await client
    .schema(SCHEMA)
    .rpc('read', { queue_name: QUEUE, n: 1, sleep_seconds: 10 });

  if (result.error) {
    throw new Error(result.error.message);
  }

  const data = result.data[0];

  if (!data) {
    return;
  }

  return {
    messageId: data.msg_id,
    allocationId: data.message.allocationId,
  };
};

export const completeAllocationReset = async (messageId: number) => {
  const client = await createAdminClient();
  await client
    .schema(SCHEMA)
    .rpc('delete', { queue_name: QUEUE, message_id: messageId });
};
