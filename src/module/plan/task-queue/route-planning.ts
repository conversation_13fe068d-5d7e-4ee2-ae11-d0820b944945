import { createAdminClient } from '@/module/app/supabase-admin';

const SCHEMA = 'pgmq_public';
const QUEUE = 'route_planning';

export const publishRouteUpdate = async (allocationId: string) => {
  const client = await createAdminClient();

  const result = await client.schema(SCHEMA).rpc('send', {
    queue_name: QUEUE,
    message: { allocationId },
  });

  if (result.error) {
    throw new Error(result.error.message);
  }
};

export const consumeRouteUpdate = async () => {
  const client = await createAdminClient();
  const result = await client
    .schema(SCHEMA)
    .rpc('read', { queue_name: QUEUE, n: 1, sleep_seconds: 10 });

  if (result.error) {
    throw new Error(result.error.message);
  }

  const data = result.data[0];

  if (!data) {
    return;
  }

  return {
    messageId: data.msg_id,
    allocationId: data.message.allocationId,
  };
};

export const archiveRouteUpdate = async (messageId: number) => {
  const client = await createAdminClient();
  await client
    .schema(SCHEMA)
    .rpc('delete', { queue_name: QUEUE, message_id: messageId });
};
