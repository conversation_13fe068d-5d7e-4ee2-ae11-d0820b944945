import { TransactionClient } from '@/module/app/db';
import { pushNotification } from '@/module/notification/core/push-notification';
import { planAllocationRepository } from '@/module/plan/repository/plan-allocation-repository';
import { profileRepository } from '@/module/profile/repository/profile-repository';

export const pushAllocationChanged = async (
  tx: TransactionClient,
  allocationId: string
) => {
  const allocation = await planAllocationRepository(tx).findById(allocationId);

  if (!allocation) {
    throw new Error('Allocation not found');
  }

  const driver = await profileRepository(tx).findById(allocation.driverId);

  if (driver?.firebaseToken) {
    await pushNotification({
      token: driver.firebaseToken,
      title: '',
      body: '',
      data: { type: 'ALLOCATION_CHANGED', allocationId },
    });
  }
};
