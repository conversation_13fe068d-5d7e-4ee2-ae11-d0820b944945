import { Maybe } from '@/lib/maybe';
import { db } from '@/module/app/db';
import { pushNotification } from '@/module/notification/core/push-notification';
import { getAllocationStops } from '@/module/plan/services/get-allocation-stops';

type AggregatedArrivedNotification = {
  clientId: string;
  firebaseToken: Maybe<string>;
  driverName: string;
};

export const pushDriverWaiting = async (
  allocationId: string,
  stopId?: string
) => {
  const stops = await getAllocationStops(db)(allocationId);
  const stopIds = stops
    .find((stop, index) => (stopId ? stop.id === stopId : index === 0))
    ?.action.map((action) => action.id.toString());

  if (!stopIds) {
    return;
  }

  const allocationStops = await db.planStop.findMany({
    where: { id: { in: stopIds } },
    include: {
      plan: {
        include: {
          stops: true,
          child: { include: { client: true } },
          driver: { include: { vehicle: true } },
        },
      },
    },
  });

  await Promise.all(
    allocationStops
      .reduce<AggregatedArrivedNotification[]>((aggregated, stop) => {
        const firebaseToken = stop.plan.child.client.firebaseToken;
        const clientId = stop.plan.child.client.id;

        // Do not notify drop offs
        if (stop.type === 'DROP_OFF') {
          return aggregated;
        }

        const existingClientStop = aggregated.findIndex(
          (a) => a.clientId === clientId
        );

        if (existingClientStop >= 0) {
          return aggregated;
        }

        aggregated.push({
          clientId,
          firebaseToken,
          driverName: stop.plan.driver?.firstName ?? '',
        });

        return aggregated;
      }, [])
      .map(async (stop) => {
        return pushNotification({
          title: 'Panda RIDE: Trip status update',
          body: `${stop.driverName} is waiting for your kid/s`,
          token: stop.firebaseToken,
        });
      })
  );
};
