import { Maybe } from '@/lib/maybe';
import { db } from '@/module/app/db';
import { pushNotification } from '@/module/notification/core/push-notification';
import { getAllocationStops } from '@/module/plan/services/get-allocation-stops';

type AggregatedDropOffNotification = {
  clientId: string;
  firebaseToken: Maybe<string>;
  child: string;
};

export const pushDropoffCompleted = async (
  allocationId: string,
  stopId: string
) => {
  const stops = await getAllocationStops(db)(allocationId);
  const stopIds = stops
    .find((stop) => stop.id === stopId)
    ?.action.map((action) => action.id.toString());

  if (!stopIds) {
    return;
  }

  const allocationStops = await db.planStop.findMany({
    where: { id: { in: stopIds } },
    include: {
      plan: {
        include: {
          stops: true,
          child: { include: { client: true } },
          driver: { include: { vehicle: true } },
        },
      },
    },
  });

  await Promise.all(
    allocationStops
      .reduce<AggregatedDropOffNotification[]>((aggregated, stop) => {
        const firebaseToken = stop.plan.child.client.firebaseToken;
        const clientId = stop.plan.child.client.id;
        const child = `${stop.plan.child.firstName} ${stop.plan.child.lastName}`;

        // Do not notify pickups
        if (stop.type === 'PICK_UP') {
          return aggregated;
        }

        const existingClientStop = aggregated.findIndex(
          (a) => a.clientId === clientId
        );

        if (existingClientStop >= 0) {
          aggregated[existingClientStop].child = [
            aggregated[existingClientStop].child,
            child,
          ].join(' and ');

          return aggregated;
        }

        aggregated.push({ clientId, firebaseToken, child });

        return aggregated;
      }, [])
      .map(async (stop) => {
        return pushNotification({
          title: 'Panda RIDE: Trip status update',
          body: `${stop.child} has just arrived at the destination.`,
          token: stop.firebaseToken,
        });
      })
  );
};
