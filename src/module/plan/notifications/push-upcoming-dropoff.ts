import { differenceInMinutes } from 'date-fns';

import { formatTime } from '@/lib/format-date';
import { Maybe } from '@/lib/maybe';
import { db } from '@/module/app/db';
import { pushNotification } from '@/module/notification/core/push-notification';
import { getAllocationStops } from '@/module/plan/services/get-allocation-stops';

type AggregatedAllocationStop = {
  clientId: string;
  eta: Date;
  etaMinutes: number;
  firebaseToken: Maybe<string>;
  child: string;
};

const makeNotificationBody = async (
  child: string,
  eta: Date,
  etaMinutes: number
): Promise<string> => {
  const etaText = `${formatTime(eta)} (${Math.abs(etaMinutes)} ${Math.abs(etaMinutes) === 1 ? 'minute' : 'minutes'}${etaMinutes < 0 ? ' ago' : ''})`;
  return `${child} are the next stop. ETA is ${etaText}`;
};

export const pushUpcomingDropoff = async (
  allocationId: string,
  stopId?: string
) => {
  const stops = await getAllocationStops(db)(allocationId);
  const stopIds = stops
    .find((stop, index) => (stopId ? stop.id === stopId : index === 0))
    ?.action.map((action) => action.id.toString());

  if (!stopIds) {
    return;
  }

  const allocationStops = await db.planStop.findMany({
    where: { id: { in: stopIds } },
    include: {
      plan: {
        include: {
          stops: true,
          child: { include: { client: true } },
        },
      },
    },
  });

  await Promise.all(
    allocationStops
      .reduce<AggregatedAllocationStop[]>((aggregated, stop) => {
        const eta = stop.eta ?? new Date();
        const etaMinutes = differenceInMinutes(eta, new Date());
        const firebaseToken = stop.plan.child.client.firebaseToken;
        const clientId = stop.plan.child.client.id;
        const child = `${stop.plan.child.firstName} ${stop.plan.child.lastName}`;

        // Do not notify pick ups
        if (stop.type === 'PICK_UP') {
          return aggregated;
        }

        const existingClientStop = aggregated.findIndex(
          (a) => a.clientId === clientId
        );

        if (existingClientStop >= 0) {
          aggregated[existingClientStop].child = [
            aggregated[existingClientStop].child,
            child,
          ].join(' and ');

          return aggregated;
        }

        aggregated.push({
          clientId,
          eta,
          etaMinutes,
          firebaseToken,
          child,
        });

        return aggregated;
      }, [])
      .map(async (stop) => {
        const body = await makeNotificationBody(
          stop.child,
          stop.eta,
          stop.etaMinutes
        );

        return pushNotification({
          title: 'Panda RIDE: Trip status update',
          body,
          token: stop.firebaseToken,
        });
      })
  );
};
