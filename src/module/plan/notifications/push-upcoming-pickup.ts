import { differenceInMinutes } from 'date-fns';
import { getTranslations } from 'next-intl/server';

import { formatTime } from '@/lib/format-date';
import { Maybe } from '@/lib/maybe';
import { db } from '@/module/app/db';
import { pushNotification } from '@/module/notification/core/push-notification';
import { getAllocationStops } from '@/module/plan/services/get-allocation-stops';

type AggregatedAllocationStop = {
  clientId: string;
  eta: Date;
  etaMinutes: number;
  firebaseToken: Maybe<string>;
  vehicleColor?: string;
  driverName: string;
  child: string;
};

const makeNotificationBody = async (
  driverName: string,
  vehicleColor: string | undefined,
  child: string,
  eta: Date,
  etaMinutes: number
): Promise<string> => {
  const t = await getTranslations({ namespace: 'color', locale: 'en' });
  // Driver information with vehicle color if available
  const driverInfo = vehicleColor
    ? `Driver ${driverName} (${t(vehicleColor as any)} panda logo)`
    : `Driver ${driverName}`;

  const etaText = `${formatTime(eta)} (${Math.abs(etaMinutes)} ${Math.abs(etaMinutes) === 1 ? 'minute' : 'minutes'}${etaMinutes < 0 ? ' ago' : ''})`;
  return `${driverInfo} is coming to pick up ${child}. ETA is ${etaText}`;
};

export const pushUpcomingPickup = async (
  allocationId: string,
  stopId?: string
) => {
  const stops = await getAllocationStops(db)(allocationId);
  const stopIds = stops
    .find((stop, index) => (stopId ? stop.id === stopId : index === 0))
    ?.action.map((action) => action.id.toString());

  if (!stopIds) {
    return;
  }

  const allocationStops = await db.planStop.findMany({
    where: { id: { in: stopIds } },
    include: {
      plan: {
        include: {
          stops: true,
          child: { include: { client: true } },
          driver: { include: { vehicle: true } },
        },
      },
    },
  });

  await Promise.all(
    allocationStops
      .reduce<AggregatedAllocationStop[]>((aggregated, stop) => {
        const eta = stop.eta ?? new Date();
        const etaMinutes = differenceInMinutes(eta, new Date());
        const firebaseToken = stop.plan.child.client.firebaseToken;
        const clientId = stop.plan.child.client.id;
        const child = `${stop.plan.child.firstName} ${stop.plan.child.lastName}`;

        // Do not notify drop offs
        if (stop.type === 'DROP_OFF') {
          return aggregated;
        }

        const existingClientStop = aggregated.findIndex(
          (a) => a.clientId === clientId
        );

        if (existingClientStop >= 0) {
          aggregated[existingClientStop].child = [
            aggregated[existingClientStop].child,
            child,
          ].join(' and ');

          return aggregated;
        }

        aggregated.push({
          clientId,
          eta,
          etaMinutes,
          firebaseToken,
          child,
          vehicleColor: stop.plan.driver?.vehicle?.color ?? undefined,
          driverName: stop.plan.driver?.firstName ?? '',
        });

        return aggregated;
      }, [])
      .map(async (stop) => {
        const body = await makeNotificationBody(
          stop.driverName,
          stop.vehicleColor,
          stop.child,
          stop.eta,
          stop.etaMinutes
        );

        return pushNotification({
          title: 'Panda RIDE: Trip status update',
          body,
          token: stop.firebaseToken,
        });
      })
  );
};
