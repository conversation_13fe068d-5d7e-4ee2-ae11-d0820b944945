import { TransactionClient } from '@/module/app/db';

export const completeStopAction = async (
  tx: TransactionClient,
  { stopId, driverId }: { stopId: string; driverId: string }
) => {
  const planStop = await tx.planStop.findFirst({
    where: { id: stopId },
  });

  if (!planStop?.statusId) {
    return tx.planStop.update({
      where: { id: stopId },
      data: {
        completedAt: new Date(),
        status: {
          create: { type: 'COMPLETED', createdById: driverId },
        },
      },
    });
  }
};
