import 'groupby-polyfill/lib/polyfill.js';

import {
  Address,
  Child,
  PlanStopStatusType,
  ScheduleStopType,
} from '@prisma/client';

import { Maybe } from '@/lib/maybe';
import { TransactionClient } from '@/module/app/db';
import { makeStopId } from '@/module/plan/services/make-stop-id';

type AllocationStop = {
  id: string;
  order: number;
  address: {
    id: string;
    createdAt: Date;
    updatedAt: Date | null;
    name: string;
    location: string | null;
    lat: number;
    lng: number;
  } | null;
  dateTime: Date;
  completed: boolean;
  arrived: boolean;
  action: {
    id: string;
    eta: Date | null;
    type: ScheduleStopType;
    planId: string;
    status: {
      id: string;
      type: PlanStopStatusType;
      createdAt: Date;
      createdById: string | null;
    } | null;
    child: Child;
    address: Maybe<Address>;
    dateTime: Date;
    order: number;
  }[];
};

export const getAllocationStops =
  (tx: TransactionClient) => async (allocationId: string) => {
    const stops = await tx.planStop.findMany({
      where: {
        plan: {
          allocationId,
          deletedAt: null,
        },
      },
      orderBy: [{ order: 'asc' }],
      include: {
        status: true,
        address: true,
        plan: {
          include: {
            child: true,
          },
        },
      },
    });

    const grouped = Object.groupBy(stops, (stop) => stop.order);

    return Object.entries(grouped).reduce<AllocationStop[]>(
      (acc, [order, actions]) => {
        const stopActions = (actions ?? [])
          // Hide drop off if pick up of the same plan has a "NO_SHOW" status
          .filter((action) => {
            if (action.type === 'DROP_OFF') {
              const pickupStatus = stops.find(
                (stop) =>
                  stop.type === 'PICK_UP' && action.plan.id === stop.plan.id
              )?.status?.type;

              if (pickupStatus === 'NO_SHOW') {
                return false;
              }
            }

            return true;
          });

        if (!stopActions.length) {
          return acc;
        }

        acc.push({
          id: makeStopId(stopActions),
          order: Number(order),
          address: stopActions[0].address,
          dateTime: stopActions[0].plan.dateTime,
          completed: (actions ?? []).every((action) => action.completedAt),
          arrived: (actions ?? []).every((action) => action.arrivedAt),
          action: stopActions
            // Map to correct format
            .map((action) => ({
              id: action.id,
              eta: action.eta,
              type: action.type,
              planId: action.plan.id,
              status: action.status,
              child: action.plan.child,
              address: action.address,
              dateTime: action.plan.dateTime,
              order: action.order,
            })),
        });

        return acc;
      },
      []
    );
  };
