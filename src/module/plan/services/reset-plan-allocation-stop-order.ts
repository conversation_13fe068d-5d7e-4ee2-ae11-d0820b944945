import { orderStops } from '@/lib/order-stops';
import { TransactionClient } from '@/module/app/db';
import { publishRouteUpdate } from '@/module/plan/task-queue/route-planning';

export const resetPlanAllocationStopOrder = async (
  tx: TransactionClient,
  allocationId: string
): Promise<void> => {
  const stops = await tx.planStop.findMany({
    where: { plan: { allocationId, deletedAt: null } },
    select: {
      id: true,
      type: true,
      address: { select: { lat: true, lng: true } },
      plan: { select: { dateTime: true } },
    },
  });

  const ordered = await orderStops(
    stops.map((stop) => ({
      id: stop.id,
      type: stop.type,
      dateTime: stop.plan.dateTime,
      address: stop.address,
    }))
  );

  await Promise.all(
    ordered.map((stop) =>
      tx.planStop.update({
        where: { id: stop.id },
        data: { order: stop.order },
      })
    )
  );

  await publishRouteUpdate(allocationId);
};
