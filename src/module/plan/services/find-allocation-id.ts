import { differenceInMinutes, endOfDay, isAfter, startOfDay } from 'date-fns';

import { compareTimeAsc } from '@/lib/compare-time-asc';
import { deduplicate } from '@/lib/deduplicate';
import { TransactionClient } from '@/module/app/db';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';

type Args = {
  dateTime: Date;
  driverId: string | null | undefined;
};

const NEW_ALLOCATION_MINUTE_THRESHOLD = 20;

/**
 * Returns the best allocation to put plan into based on dateTime and driverId.
 * @param tx
 * @param currentProfile
 * @param driverId
 * @param dateTime
 */
export const findAllocationId = async (
  tx: TransactionClient,
  { driverId, dateTime }: Args
): Promise<string | null> => {
  if (!driverId) {
    return null;
  }

  const existingPlans = (
    await tx.plan.findMany({
      where: {
        driverId,
        dateTime: { gte: startOfDay(dateTime), lte: endOfDay(dateTime) },
      },
    })
  ).toSorted((a, b) => compareTimeAsc(a.dateTime, b.dateTime));

  const existingIndex = existingPlans.findIndex(
    (plan) =>
      Math.abs(differenceInMinutes(dateTime, plan.dateTime)) <=
      NEW_ALLOCATION_MINUTE_THRESHOLD
  );

  const existing = existingPlans[existingIndex];

  if (existing) {
    return existing.allocationId;
  }

  const candidateIndex = existingPlans.findIndex(
    (plan, index) =>
      !isAfter(dateTime, plan.dateTime) || index === existingPlans.length - 1
  );

  const previousPlan = existingPlans[candidateIndex - 1];
  const nextPlan = existingPlans[candidateIndex + 1];

  const createdAllocation = await tx.planAllocation.create({
    data: { label: 'Ride', date: startOfDay(dateTime), driverId },
  });

  const isSplitting =
    previousPlan &&
    nextPlan &&
    previousPlan.allocationId === nextPlan.allocationId;

  // if splitting one allocation - prev and next allocationId is the same
  // then it should change allocation of following ones
  if (isSplitting) {
    const plansToUpdate = existingPlans.splice(existingIndex);

    // Update allocations
    await Promise.all(
      plansToUpdate.map((plan) =>
        tx.plan.update({
          where: { id: plan.id },
          data: { allocationId: createdAllocation.id },
        })
      )
    );

    // Reset all affected allocations stop order
    await Promise.all(
      deduplicate(existingPlans.map((plan) => plan.allocationId)).map(
        publishAllocationReset
      )
    );

    // Reset created allocation
    await publishAllocationReset(createdAllocation.id);
  }

  return createdAllocation.id;
};
