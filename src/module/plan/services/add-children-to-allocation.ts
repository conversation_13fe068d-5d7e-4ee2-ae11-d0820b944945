import { getHours, getMinutes, getSeconds, set } from 'date-fns';

import { AddressLookup } from '@/module/address/lib/address-lookup';
import { db } from '@/module/app/db';
import { planAllocationRepository } from '@/module/plan/repository/plan-allocation-repository';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';

type Props = {
  allocationId: string;
  childrenIds: string[];
  time: Date | string;
  pickUpAddress: AddressLookup;
  dropOffAddress: AddressLookup;
};

export const addChildrenToAllocation = async ({
  allocationId,
  childrenIds,
  time,
  pickUpAddress,
  dropOffAddress,
}: Props) => {
  const allocation = await planAllocationRepository(db).findById(allocationId);

  if (!allocation) {
    throw new Error('Allocation not found.');
  }

  const normalizedDateTime = set(allocation.date, {
    hours: getHours(time),
    minutes: getMinutes(time),
    seconds: getSeconds(time),
  });

  return db.$transaction(async (tx) => {
    await Promise.all(
      childrenIds.map((childId) =>
        tx.plan.create({
          data: {
            dateTime: normalizedDateTime,
            childId: childId,
            driverId: allocation.driverId,
            scheduleId: null,
            allocationId: allocation.id,
            stops: {
              create: [
                {
                  type: 'PICK_UP',
                  order: 0,
                  address: { create: pickUpAddress },
                },
                {
                  type: 'DROP_OFF',
                  order: 0,
                  address: { create: dropOffAddress },
                },
              ],
            },
          },
        })
      )
    );

    const updatedAllocation =
      await planAllocationRepository(tx).findById(allocationId);

    if (!updatedAllocation) {
      throw new Error('Allocation not found.');
    }

    await publishAllocationReset(allocationId);

    return updatedAllocation;
  });
};
