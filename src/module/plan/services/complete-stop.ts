import { db } from '@/module/app/db';
import { publishNotification } from '@/module/notification/task-queue/publish-notification';
import { NotificationType } from '@/module/notification/task-queue/types';
import { planAllocationRepository } from '@/module/plan/repository/plan-allocation-repository';
import { completeStopAction } from '@/module/plan/services/complete-stop-action';
import { getAllocationStops } from '@/module/plan/services/get-allocation-stops';

type Props = { allocationId: string; stopId: string; driverId: string };

export const completeStop = async ({
  allocationId,
  stopId,
  driverId,
}: Props) => {
  const repository = planAllocationRepository(db);

  const allocation = await repository.findByIdAndDriver(allocationId, driverId);

  if (!allocation) {
    throw new Error('Allocation not found.');
  }

  const stops = await getAllocationStops(db)(String(allocationId));
  const stopToCompleteIndex = stops.findIndex((stop) => stop.id === stopId);
  const stopToComplete = stops[stopToCompleteIndex];

  if (stopToComplete) {
    await db.$transaction(async (tx) => {
      await Promise.all(
        stopToComplete.action.map((action) =>
          completeStopAction(tx, {
            stopId: action.id.toString(),
            driverId,
          })
        )
      );

      const nextStop = stops[stopToCompleteIndex + 1];

      if (!nextStop) {
        await repository.complete(allocationId);
      }

      await publishNotification({
        type: NotificationType.ALLOCATION_STOP_COMPLETED,
        nextStopId: nextStop?.id.toString(),
        allocationId,
        stopId,
      });
    });
  }

  return allocation;
};
