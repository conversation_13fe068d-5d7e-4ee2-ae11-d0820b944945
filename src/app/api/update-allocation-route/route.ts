import 'server-only';

import * as Sentry from '@sentry/nextjs';
import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/module/app/db';
import { isAllocationInProgress } from '@/module/plan/lib/is-allocation-in-progress';
import { getAllocationRoute } from '@/module/plan/services/get-allocation-route';
import {
  archiveRouteUpdate,
  consumeRouteUpdate,
} from '@/module/plan/task-queue/route-planning';

export const revalidate = 0;
export const maxDuration = 800;

export async function GET(req: NextRequest) {
  if (
    process.env.NODE_ENV !== 'development' &&
    req.headers.get('Authorization') !== `Bearer ${process.env.CRON_SECRET}`
  ) {
    return NextResponse.json({ message: 'Unauthorized' });
  }

  await db.$transaction(async (tx) => {
    const update = await consumeRouteUpdate();

    if (!update) {
      return NextResponse.json({ ok: true });
    }

    const { allocationId, messageId } = update;

    const allocation = await tx.planAllocation.findUnique({
      where: { id: allocationId },
      include: { driver: { include: { driverLocation: true } } },
    });

    if (allocation && isAllocationInProgress(allocation)) {
      const driverLocation = allocation.driver.driverLocation;

      if (!driverLocation) {
        Sentry.logger.debug('Update allocation route: No driver location', {
          allocationId,
        });

        return NextResponse.json({ ok: true });
      }

      const { route, stopsEta } = await getAllocationRoute(tx)(allocationId, {
        latitude: driverLocation.latitude,
        longitude: driverLocation.longitude,
      });

      await Promise.all(
        stopsEta.map((stopEta) =>
          tx.planStop.updateMany({
            where: { id: { in: stopEta.stopIds } },
            data: { eta: stopEta.dateTime },
          })
        )
      );

      await tx.planAllocation.update({
        where: { id: allocationId },
        data: { route },
      });
    }

    await archiveRouteUpdate(messageId);
  });

  return NextResponse.json({ ok: true });
}
