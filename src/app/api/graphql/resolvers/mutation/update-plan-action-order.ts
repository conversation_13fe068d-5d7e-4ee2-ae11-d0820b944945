import { isAdmin, isAdminOrDriver, isDriver } from '@/app/api/graphql/roles';
import { MutationResolvers } from '@/app/api/graphql/types.generated';
import { db } from '@/module/app/db';
import { updatePlanActionOrder as updatePlanActionOrderService } from '@/module/plan/services/update-plan-action-order';
import { publishRouteUpdate } from '@/module/plan/task-queue/route-planning';

export const updatePlanActionOrder: NonNullable<
  MutationResolvers['updatePlanActionOrder']
> = async (_, { allocationId, planActionIds }, context) => {
  if (!isAdminOrDriver(context.profile)) {
    throw new Error('Unauthorized');
  }

  const allocation = await db.planAllocation.findUnique({
    where: {
      id: allocationId,
      ...(isDriver(context.profile) && { driverId: context.profile.id }),
      ...(isAdmin(context.profile) && { driverId: undefined }),
    },
    include: {
      driver: { include: { driverLocation: true } },
    },
  });

  if (!allocation) {
    throw new Error('Allocation not found');
  }

  const response = (await updatePlanActionOrderService(planActionIds)).map(
    (planStop) => ({
      id: planStop.id,
      type: planStop.type,
      child: planStop.plan.child,
      address: planStop.address,
      dateTime: planStop.plan.dateTime,
      order: planStop.order,
    })
  );

  await db.plan.updateMany({
    where: { allocationId },
    data: { updatedById: context.profile.id },
  });

  await publishRouteUpdate(allocationId);

  return response;
};
