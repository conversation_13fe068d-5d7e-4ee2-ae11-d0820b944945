import { isDriver } from '@/app/api/graphql/roles';
import { MutationResolvers } from '@/app/api/graphql/types.generated';
import { db } from '@/module/app/db';
import { publishRouteUpdate } from '@/module/plan/task-queue/route-planning';

export const setPlanActionStatus: MutationResolvers['setPlanActionStatus'] =
  async (_, { id, status }, context) => {
    if (!isDriver(context.profile)) {
      throw new Error('Unauthorized');
    }

    const planStop = await db.planStop.findUnique({ where: { id } });

    if (!planStop) {
      throw new Error('Stop not found');
    }

    const updatedStatus = await db.planStopStatus.create({
      data: {
        type: status,
        planStop: { connect: { id } },
        createdBy: { connect: { id: context.profile.id } },
      },
      include: {
        planStop: {
          include: { address: true, plan: { include: { child: true } } },
        },
      },
    });

    if (!updatedStatus.planStop) {
      throw new Error('Unable to update status');
    }

    const allocationId = updatedStatus.planStop?.plan.allocationId;

    if (allocationId) {
      await publishRouteUpdate(allocationId);
    }

    return {
      id,
      status: updatedStatus,
      type: updatedStatus.planStop.type,
      child: updatedStatus.planStop.plan.child,
      address: updatedStatus.planStop.address,
      order: updatedStatus.planStop.order,
      dateTime: updatedStatus.planStop.plan.dateTime,
    };
  };
