import { isDriver } from '@/app/api/graphql/roles';
import { MutationResolvers } from '@/app/api/graphql/types.generated';
import { db } from '@/module/app/db';
import { pushDriverWaiting } from '@/module/plan/notifications/push-driver-waiting';
import { getAllocationStops } from '@/module/plan/services/get-allocation-stops';

export const arrivedToStop: MutationResolvers['arrivedToStop'] = async (
  _,
  { allocationId, stopId },
  context
) => {
  if (!isDriver(context.profile)) {
    throw new Error('Unauthorized');
  }

  const allocation = await db.planAllocation.findUniqueOrThrow({
    where: { id: allocationId, driverId: context.profile.id },
    select: {
      id: true,
      label: true,
      date: true,
      createdAt: true,
      updatedAt: true,
      startedAt: true,
      completedAt: true,
      terminatedAt: true,
      terminationReason: true,
    },
  });

  const stops = await getAllocationStops(db)(String(allocationId));
  const stopIndex = stops.findIndex((stop) => stop.id === stopId);
  const stop = stops[stopIndex];

  if (stop) {
    await db.$transaction(async (tx) => {
      await Promise.all(
        stop.action.map((action) =>
          tx.planStop.update({
            where: { id: action.id.toString() },
            data: { arrivedAt: new Date() },
          })
        )
      );
    });
  }

  await pushDriverWaiting(allocationId, stopId);

  return allocation;
};
