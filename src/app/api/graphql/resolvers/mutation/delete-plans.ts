import { addMinutes } from 'date-fns';
import { match } from 'ts-pattern';

import { hasAccess } from '@/app/api/graphql/roles';
import { MutationResolvers } from '@/app/api/graphql/types.generated';
import { deduplicate } from '@/lib/deduplicate';
import { exists } from '@/lib/exists';
import { db } from '@/module/app/db';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';

export const deletePlans: NonNullable<
  MutationResolvers['deletePlans']
> = async (_, { id, role }, context) => {
  const profile = context.profile;

  if (!hasAccess([role], profile)) {
    throw new Error('Unauthorized');
  }

  const profileId = profile.id;

  return db.$transaction(async (tx) => {
    const plans = await tx.plan.findMany({
      where: {
        id: { in: id },
        ...match(role)
          .with('CLIENT', () => ({
            child: { client: { id: profileId } },
            dateTime: { gt: addMinutes(new Date(), 15) },
          }))
          .with('DRIVER', () => ({ driverId: profileId }))
          .with('ADMIN', () => ({}))
          .exhaustive(),
      },
    });

    const deletedPlans = await tx.plan.updateManyAndReturn({
      where: { id: { in: deduplicate(plans.map((plan) => plan.id)) } },
      data: { deletedAt: new Date(), updatedById: profile.id },
    });

    const allocationIds = deduplicate(
      deletedPlans.map((plan) => plan.allocationId).filter(exists)
    );

    await Promise.all(allocationIds.map(publishAllocationReset));

    return deletedPlans;
  });
};
