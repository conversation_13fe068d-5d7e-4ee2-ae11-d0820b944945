import { isSameDay } from 'date-fns';

import { isDriver } from '@/app/api/graphql/roles';
import { MutationResolvers } from '@/app/api/graphql/types.generated';
import { db } from '@/module/app/db';
import { publishNotification } from '@/module/notification/task-queue/publish-notification';
import { NotificationType } from '@/module/notification/task-queue/types';
import { publishRouteUpdate } from '@/module/plan/task-queue/route-planning';

export const startAllocation: MutationResolvers['startAllocation'] = async (
  _,
  { id, startLocation },
  context
) => {
  const profile = context.profile;

  if (!isDriver(profile)) {
    throw new Error('Unauthorized');
  }

  const allocation = await db.planAllocation.findUniqueOrThrow({
    where: { id, driverId: profile.id },
  });

  if (!isSameDay(allocation.date, new Date())) {
    // eslint-disable-next-line
    throw new Error("Can only start today's allocations.");
  }

  if (allocation.completedAt) {
    throw new Error('Cannot start completed allocation');
  }

  if (allocation.startedAt) {
    throw new Error('Cannot start ongoing allocation');
  }

  return db.$transaction(async (tx) => {
    const updatedAllocation = await tx.planAllocation.update({
      where: { id, driverId: profile.id },
      data: { startedAt: new Date() },
      select: {
        id: true,
        label: true,
        date: true,
        createdAt: true,
        updatedAt: true,
        startedAt: true,
        completedAt: true,
      },
    });

    await tx.driverLocation.upsert({
      where: { driverId: profile.id },
      update: startLocation,
      create: {
        driverId: profile.id,
        timestamp: new Date(),
        ...startLocation,
      },
    });

    await publishRouteUpdate(updatedAllocation.id);

    await publishNotification({
      type: NotificationType.ALLOCATION_STARTED,
      allocationId: updatedAllocation.id,
    });

    return updatedAllocation;
  });
};
