import 'server-only';

import { flatten } from '@graphql-codegen/visitor-plugin-common';
import { MonitoringType } from '@prisma/client';
import { endOfDay, startOfDay } from 'date-fns';
import { NextRequest, NextResponse } from 'next/server';

import { deduplicate } from '@/lib/deduplicate';
import { db } from '@/module/app/db';
import { publishAllocationReset } from '@/module/plan/task-queue/allocation-reset';

export const revalidate = 0;

export async function GET(req: NextRequest) {
  if (
    process.env.NODE_ENV !== 'development' &&
    req.headers.get('Authorization') !== `Bearer ${process.env.CRON_SECRET}`
  ) {
    return NextResponse.json({ message: 'Unauthorized' });
  }

  await db.$transaction(
    async (tx) => {
      const monitoring = await tx.monitoring.upsert({
        where: { type: MonitoringType.HOLIDAY },
        update: {},
        create: {
          type: MonitoringType.HOLIDAY,
          updatedAt: new Date(0),
        },
      });

      const childSchools = await tx.childSchool.findMany({
        where: {
          OR: [
            { updatedAt: { gte: monitoring.updatedAt } },
            {
              school: {
                holiday: { some: { updatedAt: { gte: monitoring.updatedAt } } },
              },
            },
          ],
        },
        include: {
          school: { include: { holiday: true } },
        },
      });

      const updatedPlans = await Promise.all(
        childSchools.map((childSchool) =>
          db.plan.updateManyAndReturn({
            where: {
              OR: childSchool.school.holiday.map((holiday) => ({
                child: {
                  id: childSchool.childId,
                },
                dateTime: {
                  gte: startOfDay(holiday.startDate),
                  lte: endOfDay(holiday.endDate),
                },
              })),
            },
            data: {
              deletedAt: new Date(),
            },
          })
        )
      );

      await Promise.all(
        deduplicate(flatten(updatedPlans).map((plan) => plan.allocationId)).map(
          publishAllocationReset
        )
      );

      await tx.monitoring.update({
        where: { type: MonitoringType.HOLIDAY },
        data: { updatedAt: new Date() },
      });
    },
    { timeout: 60_000 }
  );

  return NextResponse.json({ ok: true });
}
