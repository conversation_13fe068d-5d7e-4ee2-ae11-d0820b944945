import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { validateAuthorization } from '@/module/notification/middleware/auth';
import { NotificationProcessor } from '@/module/notification/services/notification-processor';
import { createErrorResponse, createSuccessResponse } from '@/module/notification/utils/response';

export const revalidate = 0;
export const maxDuration = 800;

/**
 * GET /api/notification
 *
 * Processes notifications from the message queue.
 * This endpoint is called by cron jobs to handle allocation-related notifications.
 */
export async function GET(req: NextRequest) {
  try {
    // Validate authorization
    const authResult = validateAuthorization(req);
    if (!authResult.success) {
      return createErrorResponse(authResult.error || 'Unauthorized', 401);
    }

    // Process notifications
    const processor = new NotificationProcessor();
    const result = await processor.processNext();

    return createSuccessResponse(result);
  } catch (error) {
    console.error('Error processing notification:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
