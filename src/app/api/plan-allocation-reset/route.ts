import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/module/app/db';
import { pushAllocationChanged } from '@/module/plan/notifications/push-allocation-changed';
import { resetPlanAllocationStopOrder } from '@/module/plan/services/reset-plan-allocation-stop-order';
import {
  completeAllocationReset,
  consumeAllocationReset,
} from '@/module/plan/task-queue/allocation-reset';

export const revalidate = 0;
export const maxDuration = 800;

export async function GET(req: NextRequest) {
  if (
    process.env.NODE_ENV !== 'development' &&
    req.headers.get('Authorization') !== `Bearer ${process.env.CRON_SECRET}`
  ) {
    return NextResponse.json({ message: 'Unauthorized' });
  }

  await db.$transaction(async (tx) => {
    const message = await consumeAllocationReset();

    if (!message) {
      return NextResponse.json({ ok: true });
    }

    const { allocationId, messageId } = message;

    await resetPlanAllocationStopOrder(tx, allocationId);
    await pushAllocationChanged(tx, allocationId);
    await completeAllocationReset(messageId);
  });

  return NextResponse.json({ ok: true });
}
